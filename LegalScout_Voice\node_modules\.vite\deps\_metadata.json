{"hash": "df2127a2", "browserHash": "689275fa", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e43d10e6", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "224e63a9", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "b3597715", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "4c36234d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f6a1e89e", "needsInterop": true}, "@modelcontextprotocol/sdk/client/index.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/index.js", "file": "@modelcontextprotocol_sdk_client_index__js.js", "fileHash": "d35854a1", "needsInterop": false}, "@modelcontextprotocol/sdk/client/sse.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/sse.js", "file": "@modelcontextprotocol_sdk_client_sse__js.js", "fileHash": "0d78d2b4", "needsInterop": false}, "@modelcontextprotocol/sdk/client/streamableHttp.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js", "file": "@modelcontextprotocol_sdk_client_streamableHttp__js.js", "fileHash": "506b4c04", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "a121b5f5", "needsInterop": false}, "@vapi-ai/web": {"src": "../../@vapi-ai/web/dist/vapi.js", "file": "@vapi-ai_web.js", "fileHash": "b641211e", "needsInterop": true}, "jose": {"src": "../../jose/dist/webapi/index.js", "file": "jose.js", "fileHash": "73c358c2", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "f56a602b", "needsInterop": true}, "papaparse": {"src": "../../papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "c7ac1b85", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "84b8dfd1", "needsInterop": true}, "react-dom/server": {"src": "../../react-dom/server.browser.js", "file": "react-dom_server.js", "fileHash": "77e85a6c", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "60b28b8e", "needsInterop": false}, "react-icons/md": {"src": "../../react-icons/md/index.mjs", "file": "react-icons_md.js", "fileHash": "3fe3b992", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "c0490e96", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "91e8e95d", "needsInterop": false}, "rehype-raw": {"src": "../../rehype-raw/index.js", "file": "rehype-raw.js", "fileHash": "a418286e", "needsInterop": false}, "rehype-sanitize": {"src": "../../rehype-sanitize/index.js", "file": "rehype-sanitize.js", "fileHash": "6c96d06b", "needsInterop": false}, "remark-gfm": {"src": "../../remark-gfm/index.js", "file": "remark-gfm.js", "fileHash": "82ef72ed", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "f34c8fe9", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "f07acaa3", "needsInterop": false}}, "chunks": {"browser-2H53BKCB": {"file": "browser-2H53BKCB.js"}, "browser-L3WHH2Q2": {"file": "browser-L3WHH2Q2.js"}, "chunk-CUTKQHYX": {"file": "chunk-CUTKQHYX.js"}, "chunk-B6J4VDVC": {"file": "chunk-B6J4VDVC.js"}, "chunk-ZKFSI6YS": {"file": "chunk-ZKFSI6YS.js"}, "chunk-WLB7R6ZN": {"file": "chunk-WLB7R6ZN.js"}, "chunk-EFHT7PV7": {"file": "chunk-EFHT7PV7.js"}, "chunk-3TH3G7JX": {"file": "chunk-3TH3G7JX.js"}, "chunk-A5ED6EHL": {"file": "chunk-A5ED6EHL.js"}, "chunk-Q72EVS5P": {"file": "chunk-Q72EVS5P.js"}, "chunk-73YGM6QR": {"file": "chunk-73YGM6QR.js"}, "chunk-2N3A5BUM": {"file": "chunk-2N3A5BUM.js"}, "chunk-SJSTY3YX": {"file": "chunk-SJSTY3YX.js"}, "chunk-FYR2ONTC": {"file": "chunk-FYR2ONTC.js"}, "chunk-C3M7BXFS": {"file": "chunk-C3M7BXFS.js"}}}